//! Main renderer implementation.
//! 
//! Coordinates rendering operations, manages render pipelines,
//! and provides high-level rendering interface.

use std::sync::Arc;
use std::collections::HashMap;
use metal::{RenderPassDescriptor, MTLLoadAction, MTLStoreAction};
use glam::Vec4;

use crate::{
    Result,
    core::{MetalDevice, EngineConfig},
    renderer::{RenderPipeline, PipelineType, RenderCommand, shaders::ShaderManager},
    math::Camera,
};

/// Main renderer
pub struct Renderer {
    /// Metal device reference
    device: Arc<MetalDevice>,
    /// Render pipelines cache
    pipelines: HashMap<PipelineType, RenderPipeline>,
    /// Shader manager
    shader_manager: ShaderManager,
    /// Current viewport size
    viewport_width: f32,
    viewport_height: f32,
    /// Default camera for 3D rendering
    camera: Camera,
    /// Clear color
    clear_color: Vec4,
}

impl Renderer {
    /// Create a new renderer
    pub fn new(device: Arc<MetalDevice>, config: &EngineConfig) -> Result<Self> {
        log::info!("Initializing renderer...");

        // Initialize shader manager and load default shaders
        let mut shader_manager = ShaderManager::new(&device);
        shader_manager.load_default_shaders()?;

        let mut pipelines = HashMap::new();

        // Create 2D pipeline
        let pipeline_2d = RenderPipeline::new_with_library(
            &device,
            PipelineType::Geometry2D,
            shader_manager.get_library("default").unwrap(),
            "vertex_2d",
            "fragment_2d",
        )?;
        pipelines.insert(PipelineType::Geometry2D, pipeline_2d);

        // Create 3D pipeline
        let pipeline_3d = RenderPipeline::new_with_library(
            &device,
            PipelineType::Geometry3D,
            shader_manager.get_library("default").unwrap(),
            "vertex_3d",
            "fragment_3d",
        )?;
        pipelines.insert(PipelineType::Geometry3D, pipeline_3d);

        // Create default camera
        let camera = Camera::default_perspective(config.aspect_ratio());

        log::info!("Renderer initialized successfully");

        Ok(Self {
            device,
            pipelines,
            shader_manager,
            viewport_width: config.window_width as f32,
            viewport_height: config.window_height as f32,
            camera,
            clear_color: Vec4::new(0.1, 0.1, 0.1, 1.0),
        })
    }

    /// Render a frame
    pub fn render_frame(&mut self, drawable: &metal::MetalDrawableRef, delta_time: f32) -> Result<()> {
        // Create command buffer
        let command_buffer = self.device.new_command_buffer()?;
        command_buffer.set_label("Main Render");

        // Create render pass descriptor
        let render_pass_descriptor = RenderPassDescriptor::new();
        let color_attachment = render_pass_descriptor.color_attachments().object_at(0).unwrap();
        color_attachment.set_texture(Some(drawable.texture()));
        color_attachment.set_load_action(MTLLoadAction::Clear);
        color_attachment.set_store_action(MTLStoreAction::Store);
        color_attachment.set_clear_color(metal::MTLClearColor::new(
            self.clear_color.x as f64,
            self.clear_color.y as f64,
            self.clear_color.z as f64,
            self.clear_color.w as f64,
        ));

        // Create render command encoder
        let encoder = command_buffer.new_render_command_encoder(&render_pass_descriptor);
        encoder.set_label("Main Render Pass");

        // Set viewport
        encoder.set_viewport(metal::MTLViewport {
            originX: 0.0,
            originY: 0.0,
            width: self.viewport_width as f64,
            height: self.viewport_height as f64,
            znear: 0.0,
            zfar: 1.0,
        });

        // Render some basic demo content
        self.render_demo_content(&encoder, delta_time)?;

        encoder.end_encoding();

        // Present the drawable
        command_buffer.present_drawable(drawable);

        // Commit the command buffer
        command_buffer.commit();

        Ok(())
    }

    /// Render demo content (a simple animated triangle)
    fn render_demo_content(&self, _encoder: &metal::RenderCommandEncoderRef, delta_time: f32) -> Result<()> {
        // For now, just log that we're rendering
        use std::sync::atomic::{AtomicU32, Ordering};
        static FRAME_COUNT: AtomicU32 = AtomicU32::new(0);

        let count = FRAME_COUNT.fetch_add(1, Ordering::Relaxed);
        if count % 60 == 0 {
            log::info!("Rendering frame {} (delta: {:.3}ms)", count, delta_time * 1000.0);
        }

        // TODO: Implement actual geometry rendering
        // This would involve:
        // 1. Setting the render pipeline state
        // 2. Setting vertex buffers
        // 3. Setting uniforms
        // 4. Drawing primitives

        Ok(())
    }

    /// Render frame in fallback mode (without drawable)
    pub fn render_frame_fallback(&mut self, delta_time: f32) -> Result<()> {
        // Just run the demo content logic without actual rendering
        self.render_demo_content_fallback(delta_time)?;
        Ok(())
    }

    /// Demo content for fallback mode
    fn render_demo_content_fallback(&self, delta_time: f32) -> Result<()> {
        use std::sync::atomic::{AtomicU32, Ordering};
        static FRAME_COUNT: AtomicU32 = AtomicU32::new(0);

        let count = FRAME_COUNT.fetch_add(1, Ordering::Relaxed);
        if count % 120 == 0 {
            log::info!("Engine running - frame {} (delta: {:.3}ms) - Graphics engine architecture working!",
                      count, delta_time * 1000.0);
        }
        Ok(())
    }

    /// Handle window resize
    pub fn handle_resize(&mut self, width: u32, height: u32) -> Result<()> {
        self.viewport_width = width as f32;
        self.viewport_height = height as f32;
        
        // Update camera aspect ratio
        self.camera.aspect_ratio = width as f32 / height as f32;
        
        log::info!("Renderer viewport updated to {}x{}", width, height);
        Ok(())
    }

    /// Set clear color
    pub fn set_clear_color(&mut self, color: Vec4) {
        self.clear_color = color;
    }

    /// Get the camera
    pub fn camera(&self) -> &Camera {
        &self.camera
    }

    /// Get mutable camera
    pub fn camera_mut(&mut self) -> &mut Camera {
        &mut self.camera
    }

    /// Execute a render command
    pub fn execute_command(&mut self, _command: &RenderCommand) -> Result<()> {
        // TODO: Implement command execution
        Ok(())
    }

    /// Get viewport size
    pub fn viewport_size(&self) -> (f32, f32) {
        (self.viewport_width, self.viewport_height)
    }
}
