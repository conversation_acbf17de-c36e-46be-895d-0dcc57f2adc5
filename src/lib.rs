//! # Graphics Engine
//! 
//! A high-performance Metal-based graphics engine for macOS ARM architecture.
//! 
//! This engine focuses on:
//! - **Performance**: Optimized Metal API usage with minimal overhead
//! - **Reliability**: Comprehensive error handling and memory safety
//! - **Code Quality**: Clean architecture with proper separation of concerns

#![deny(unsafe_op_in_unsafe_fn)]
#![warn(missing_docs, clippy::all, clippy::pedantic)]
#![allow(clippy::module_name_repetitions)]

pub mod core;
pub mod renderer;
pub mod geometry;
pub mod math;
pub mod error;

pub use error::{EngineError, Result};

/// Re-export commonly used types for convenience
pub mod prelude {
    pub use crate::core::{Engine, EngineConfig};
    pub use crate::renderer::{Renderer, RenderCommand};
    pub use crate::geometry::{Vertex2D, Vertex3D, Mesh};
    pub use crate::math::{Transform2D, Transform3D};
    pub use crate::error::{EngineError, Result};
    pub use glam::{Vec2, Vec3, Vec4, Mat4, Quat};
}
